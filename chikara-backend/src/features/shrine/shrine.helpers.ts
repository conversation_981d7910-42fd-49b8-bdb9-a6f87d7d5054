import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";
import { UserModel } from "../../lib/db.js";
import { getToday } from "../../utils/dateHelpers.js";
import { logger } from "../../utils/log.js";
import gameConfig from "../../config/gameConfig.js";

const SHRINE_BUFFS = [
    {
        buffType: "rareDrops",
        description: "Increases rare item drop chance",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
    {
        buffType: "craftSpeed",
        description: "Reduces crafting time of all items",
        primaryValue: 0.6,
        secondaryValue: 0.8,
    },
    {
        buffType: "damage",
        description: "Increases all damage done",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "exp",
        description: "Increases all EXP received",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "armour",
        description: "Increases armour",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "jail",
        description: "Reduces jail time by 50%",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "mission",
        description: "Reduces mission durations by 50%",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "yenEarnings",
        description: "Increases all Yen from encounters and job payouts",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "auctionFees",
        description: "Reduces auction listing fees",
        primaryValue: 0.5,
        secondaryValue: 0.75,
    },
    {
        buffType: "exploreSpawn",
        description: "Increases spawn rate of explore map nodes",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "gatheringAmount",
        description: "Increases amount gathered from gathering nodes",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
];

export const hasUserDonatedMinimumForSecondaryBuffs = async (userId: number, date: Date) => {
    const userDonation = await ShrineRepository.findUserDonationForDate(userId, date);
    if (!userDonation) {
        return false;
    }
    // Require at least the minimum donation amount for secondary buffs
    return userDonation.amount >= gameConfig.SHRINE_MINIMUM_DONATION;
};

export const dailyBuffIsActive = async (type: string, userId: number) => {
    const today = getToday();
    const dailyShrine = await ShrineRepository.findDailyShrineGoalWithReached(today);

    if (dailyShrine && dailyShrine.buffRewards) {
        const parsedRewards = dailyShrine.buffRewards;
        if (parsedRewards[type]) {
            const buffData = parsedRewards[type];

            // Check if this is a primary buff (global for all players)
            const primaryBuffTypes = Object.keys(parsedRewards).filter((buffType) => {
                return (
                    parsedRewards[buffType].value === SHRINE_BUFFS.find((b) => b.buffType === buffType)?.primaryValue
                );
            });

            const isPrimaryBuff = primaryBuffTypes.includes(type);

            // Primary buffs are available to all players
            if (isPrimaryBuff) {
                return buffData.value;
            }

            // Secondary buffs require the user to have donated minimum amount
            if (userId) {
                const userHasDonated = await hasUserDonatedMinimumForSecondaryBuffs(userId, today);
                if (userHasDonated) {
                    return buffData.value;
                }
            }

            // User hasn't donated enough for secondary buffs
            return null;
        }
    }
    return null;
};

export const dailyShrineDonations = async (date: Date) => {
    return await ShrineRepository.getDailyDonations(date);
};

export const getDailyShrineGoal = async (date: Date) => {
    return await ShrineRepository.findDailyShrineGoal(date);
};

export const addToDailyShrineGoal = async (user: UserModel, amount: number) => {
    const today = getToday();
    const dailyShrineGoal = await getDailyShrineGoal(today);
    if (!dailyShrineGoal) {
        return false;
    }
    dailyShrineGoal.donationAmount += amount;

    const existingDonation = await ShrineRepository.findUserDonationForDate(user.id, today);

    if (existingDonation) {
        await ShrineRepository.updateDonation(existingDonation.id, existingDonation.amount + amount);
    } else {
        await ShrineRepository.createDonation(user.id, today, amount);
    }

    if (!dailyShrineGoal.goalReached && dailyShrineGoal.donationAmount >= dailyShrineGoal.donationGoal) {
        dailyShrineGoal.goalReached = true;

        await ChatHelper.SendAnnouncementMessage(
            "shrineGoalReached",
            `The Shrine daily donation goal has been reached. Global buffs are now active!`
        );
    }

    return await ShrineRepository.updateShrineGoal(dailyShrineGoal.id, {
        donationAmount: dailyShrineGoal.donationAmount,
        goalReached: dailyShrineGoal.goalReached,
    });
};

export const getRandomDailyBuffs = () => {
    const primaryBuff = SHRINE_BUFFS[Math.floor(Math.random() * SHRINE_BUFFS.length)];

    // Filter out the primary buff type to ensure different buff types
    const availableSecondaryBuffs = SHRINE_BUFFS.filter((buff) => buff.buffType !== primaryBuff.buffType);
    const secondaryBuff = availableSecondaryBuffs[Math.floor(Math.random() * availableSecondaryBuffs.length)];

    const primary = {
        buffType: primaryBuff.buffType,
        description: primaryBuff.description,
        value: primaryBuff.primaryValue,
    };

    const secondary = {
        buffType: secondaryBuff.buffType,
        description: secondaryBuff.description,
        value: secondaryBuff.secondaryValue,
    };

    return { [primary.buffType]: primary, [secondary.buffType]: secondary };
};

export const announceDonationGoalReset = async (donationGoal: number | string) => {
    try {
        await ChatHelper.SendAnnouncementMessage("shrineGoalReset", JSON.stringify({ goal: donationGoal }));
    } catch (error) {
        logger.error(`Failed to send chat message: ${error}`);
    }
};
