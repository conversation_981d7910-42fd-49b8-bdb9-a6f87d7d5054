import { adminClient, usernameClient } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";
const callbackURL = baseURL + "/callback";
2025-07-23T13:37:54.807Z ERROR [Better Auth]: PrismaClientValidationError: 
Invalid `db[model].create()` invocation in
/Users/<USER>/Developer/GitHub/chikara-academy/node_modules/.bun/better-auth@1.3.2/node_modules/better-auth/dist/ad
apters/prisma-adapter/index.mjs:92:32

  89     `Model ${model} does not exist in the database. If you haven't generated the Prisma client, you need to r
un 'npx prisma generate'`
  90   );
  91 }
→ 92 return await db[model].create({
       data: {
         username: "<PERSON><PERSON><PERSON>",
         email: "<EMAIL>",
         emailVerified: true,
         avatar: "https://cdn.discordapp.com/avatars/136602344241496065/8778a7e060901f20609cdbf9bb47396b.png",
         createdAt: new Date("2025-07-23T13:37:54.789Z"),
         updatedAt: new Date("2025-07-23T13:37:54.789Z"),
         userType: "student",
     +   displayUsername: String
       },
       select: undefined
     })

Argument `displayUsername` is missing.
2025-07-23T13:37:54.807Z ERROR [Better Auth]: unable_to_create_user
export const authClient = createAuthClient({
    baseURL: `${baseURL}/auth`,
    plugins: [adminClient(), usernameClient()],
    fetchOptions: {
        onError: (ctx) => {
            console.error("Auth Error:", ctx.error);
        },
    },
});

export const signInDiscord = async (username?: string, email?: string) => {
    const callbackParams = new URLSearchParams({ auth: "discord" });
    let state: string | undefined;

    if (username && email) {
        state = JSON.stringify({
            username,
            email,
            isRegistration: true,
        });
    }

    const data = await authClient.signIn.social({
        provider: "discord",
        callbackURL: `${callbackURL}?${callbackParams.toString()}`,
        ...(state && { state }),
    });
    return data;
};

export const signInGoogle = async (username?: string, email?: string) => {
    const callbackParams = new URLSearchParams({ auth: "google" });
    let state: string | undefined;

    if (username && email) {
        state = JSON.stringify({
            username,
            email,
            isRegistration: true,
        });
    }

    const data = await authClient.signIn.social({
        provider: "google",
        callbackURL: `${callbackURL}?${callbackParams.toString()}`,
        ...(state && { state }),
    });
    return data;
};

// Export commonly used hooks and methods
export const { useSession, signIn, signOut, signUp, forgetPassword, resetPassword, changePassword, changeEmail } =
    authClient;
